# 项目说明
> 本项目采用 uni-app 为多端场景， 当前仅适配微信小程序，后续视情况适配其他端。
> 本项目基于 uni-app + typescript 开发， 运行时需要配置开发者工具， 
> 1. 将管用小程序原生微信开发者工具打开项目，登录完成，将storage的token信息复制到这个项目的开发者工具storage中【保持登录态】
> 样式单位统一用rpx, 三方组件及历史组件如果调整也统一用rpx

# 运行项目
> 测试环境
> ```shell 
> $: npm run dev:mp-weixin 
> $: npm run dev:h5 

> ```
> 审核环境
> ```shell 
> $: npm run audit:mp-weixin
> ```
> 生产环境
> ```shell 
> $: npm run build:mp-weixin 
> ```



# 工具类使用
> 1. 使用JMLog代替console.log， 方便调试， 并且可以根据环境配置是否打印日志，默认正式环境不打印日志