import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// Define the UserData interface based on the one in index.vue
interface UserData {
  id: string;
  full_name?: string;
  email?: string;
  lingdou_balance?: number;
  points_balance?: number;
  vip_level?: string;
  total_lingdou_used?: number;
  total_points_earned?: number;
}

export const useUserStore = defineStore('user', () => {
  const currentUser = ref<UserData | null>(null);

  function setUser(newUserData: UserData) {
    currentUser.value = newUserData;
  }

  function clearUser() {
    currentUser.value = null;
  }

  const user = computed(() => currentUser.value);

  return { user, setUser, clearUser };
});