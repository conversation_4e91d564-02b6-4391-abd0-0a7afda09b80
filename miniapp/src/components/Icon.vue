<template>
  <!-- <component :is="iconComponent" :size="size" :color="color" /> -->
   <view />
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
// Import all Lucide icons used in Home.vue and other pages
import {
  Home, Trophy, User, BookOpen, Target, Flame, CheckCircle, Star, Zap, BrainCircuit, Mic, Sparkles,
  TrendingUp, Award, ChevronRight, Play,
  // Add any other icons used across your app here
} from 'lucide-vue-next';

interface IconProps {
  name: string;
  size?: string | number;
  color?: string;
}

const props = defineProps<IconProps>();

const iconComponent = computed(() => {
  switch (props.name) {
    case 'Home': return Home;
    case 'Trophy': return Trophy;
    case 'User': return User;
    case 'BookOpen': return BookOpen;
    case 'Target': return Target;
    case 'Flame': return Flame;
    case 'CheckCircle': return CheckCircle;
    case 'Star': return Star;
    case 'Zap': return Zap;
    case 'BrainCircuit': return BrainCircuit;
    case 'Mic': return Mic;
    case 'Sparkles': return Sparkles;
    case 'TrendingUp': return TrendingUp;
    case 'Award': return Award;
    case 'ChevronRight': return ChevronRight;
    case 'Play': return Play;
    // Add more cases for other icons as needed
    default:
      console.warn(`Icon '${props.name}' not found. Please add it to Icon.vue.`);
      return null; // Or a fallback icon
  }
});
</script>