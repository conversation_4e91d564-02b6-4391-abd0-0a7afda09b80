<template>
  <view class="container">
    <!-- 今日行动面板 -->
    <view class="header-panel">
      <view class="content-wrapper">
        <!-- 问候语 -->
        <view class="greeting-section">
          <text class="greeting-text">
            {{ greeting }} {{ user?.full_name?.split(' ')[0] || '学员' }}
            <text class="wave-emoji">👋</text>
          </text>
          <text class="tip-text">{{ dailyTip }}</text>
        </view>

        <!-- 核心行动卡片 -->
        <view v-if="nextAction" class="action-card">
          <view class="action-content">
            <view class="icon-wrapper">
              <Icon :name="nextAction.icon" size="16" color="#3b82f6" />
            </view>
            <view class="action-info">
              <view class="action-header">
                <text class="action-title">{{ nextAction.title }}</text>
                <navigator :url="nextAction.url" class="action-button">
                  {{ nextAction.buttonText }}
                </navigator>
              </view>
              <navigator :url="nextAction.url">
                <text class="action-subtitle">{{ nextAction.subtitle }}</text>
              </navigator>
            </view>
          </view>
        </view>

        <!-- 训练目标 -->
        <view class="goal-card">
          <view class="goal-header">
            <view class="goal-stats">
              <Icon name="Flame" size="12" color="#fb923c" />
              <text class="goal-text">今日 {{ dailyGoalCompleted }}/{{ DAILY_GOAL }} · 本周 {{ weeklyTraining }}/{{ WEEKLY_GOAL }}</text>
            </view>
            <text class="goal-status">
              {{ dailyGoalCompleted >= DAILY_GOAL ? '✅ 达标' : weeklyTraining >= WEEKLY_GOAL ? '🔥 周达标' : '💪 加油' }}
            </text>
          </view>
          <view class="progress-section">
            <view class="progress-item">
              <text class="progress-label">今日</text>
              <view class="progress-bar">
                <view class="progress-fill daily" :style="{ width: Math.min((dailyGoalCompleted / DAILY_GOAL) * 100, 100) + '%' }"></view>
              </view>
            </view>
            <view class="progress-item">
              <text class="progress-label">本周</text>
              <view class="progress-bar">
                <view class="progress-fill weekly" :style="{ width: Math.min((weeklyTraining / WEEKLY_GOAL) * 100, 100) + '%' }"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="main-content">
      <!-- AI训练特色区块 -->
      <view class="ai-section">
        <view class="ai-card">
          <view class="ai-header">
            <view class="ai-title-wrapper">
              <text class="ai-emoji">🎯</text>
              <text class="ai-title">AI智能训练</text>
            </view>
            <view class="ai-badge">
              <Icon name="Sparkles" size="12" color="white" />
              <text class="ai-badge-text">AI驱动</text>
            </view>
          </view>
          <text class="ai-description">🚀 AI实时分析你的回答，提供个性化指导</text>
          <view class="ai-items">
            <navigator url="/pages/TopicDetail/index?id=晋升汇报">
              <view class="ai-main-item">
                <view class="ai-main-content">
                  <Icon name="BrainCircuit" size="20" color="white" />
                  <view class="ai-main-info">
                    <text class="ai-main-title">晋升汇报AI训练</text>
                    <text class="ai-main-subtitle">AI分析你的表达能力</text>
                  </view>
                </view>
                <view class="ai-hot-badge">🔥 热门</view>
              </view>
            </navigator>
            <view class="ai-sub-items">
              <navigator url="/pages/TopicDetail/index?id=情商段位来比拼">
                <view class="ai-sub-item ai-sub-item-1">
                  <Icon name="Mic" size="16" color="white" />
                  <text class="ai-sub-title">情商AI测试</text>
                  <text class="ai-sub-subtitle">智能情商评估</text>
                </view>
              </navigator>
              <navigator url="/pages/TrainingHub/index?tab=ai">
                <view class="ai-sub-item ai-sub-item-2">
                  <Icon name="Zap" size="16" color="white" />
                  <text class="ai-sub-title">更多AI训练</text>
                  <text class="ai-sub-subtitle">探索全部AI功能</text>
                </view>
              </navigator>
            </view>
          </view>
          <view class="ai-footer">
            <text class="ai-footer-text"><text class="ai-highlight">AI技术驱动</text> 让训练更智能更有效</text>
          </view>
        </view>

        <!-- 我的成长足迹 -->
        <view class="stats-section">
          <text class="section-title">我的成长足迹</text>
          <view class="stats-card">
            <view class="stats-grid">
              <view class="stat-item">
                <Icon name="TrendingUp" size="20" color="#3b82f6" />
                <text class="stat-number">{{ stats.totalTraining }}</text>
                <text class="stat-label">总训练</text>
              </view>
              <view class="stat-item">
                <Icon name="Target" size="20" color="#3b82f6" />
                <text class="stat-number">{{ stats.averageAccuracy }}%</text>
                <text class="stat-label">平均准确率</text>
              </view>
              <view class="stat-item">
                <Icon name="Award" size="20" color="#3b82f6" />
                <text class="stat-number">{{ stats.masteredTopics }}</text>
                <text class="stat-label">精通主题</text>
              </view>
            </view>
          </view>

          <navigator url="/pages/Leaderboard/index">
            <view class="leaderboard-card">
              <view class="leaderboard-content">
                <view class="leaderboard-info">
                  <Icon name="Trophy" size="20" color="#facc15" />
                  <view class="leaderboard-text">
                    <text class="leaderboard-title">挑战英雄榜</text>
                    <text class="leaderboard-subtitle">看看你的全国排名</text>
                  </view>
                </view>
                <Icon name="ChevronRight" size="16" color="#bfdbfe" />
              </view>
            </view>
          </navigator>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
// import { useRouter } from 'vue-router'; // For web, uni-app uses uni.navigateTo
import { onShow } from '@dcloudio/uni-app'; // uni-app specific lifecycle hook
import { useUserStore } from '@/stores/user';

// Assuming these are your API entities or mock data structures
// import { Category, UserProgress, User, TrainingSession, Topic, Question } from '@/api/entities';
import { format } from 'date-fns';

// Define interfaces for better type safety
interface UserData {
  id: string;
  full_name?: string;
  email?: string;
  lingdou_balance?: number;
  points_balance?: number;
  vip_level?: string;
  total_lingdou_used?: number;
  total_points_earned?: number;
}

interface UserProgressData {
  id: string;
  user_id: string;
  topic_id: string;
  knowledge_learned?: boolean;
  mastery_level?: string;
  training_count?: number;
  accuracy_rate?: number;
  best_score?: number;
  correct_answers?: number;
  total_questions?: number;
  last_trained_date?: string;
}

interface TopicData {
  id: string;
  name: string;
  order?: number;
  // Add other topic properties as needed
}

interface TrainingSessionData {
  id: string;
  user_id: string;
  topic_id: string;
  completion_date?: string;
  // Add other session properties as needed
}

interface QuestionData {
  id: string;
  type: 'choice' | 'voice';
  topic_name: string;
  // Add other question properties as needed
}

interface NextAction {
  type: 'learn' | 'train';
  topicId: string;
  title: string;
  subtitle: string;
  buttonText: string;
  icon: string; // Changed to string to match Icon component prop
  url: string;
}

// Constants
const DAILY_GOAL = 1;
const WEEKLY_GOAL = 5;

// Reactive State
const categories = ref<any[]>([]); // Replace any[] with actual Category type if available
const userStore = useUserStore();
const dailyGoalCompleted = ref(0);
const weeklyTraining = ref(0);
const stats = ref({ totalTraining: 0, averageAccuracy: 0, masteredTopics: 0 });
const nextAction = ref<NextAction | null>(null);
const dailyTip = ref('');
const aiTrainingTopics = ref<Set<string>>(new Set());

// Computed property to get user from store for the template
const user = computed(() => userStore.user);

// Computed Properties
const greeting = computed(() => {
  const hour = new Date().getHours();
  if (hour < 12) return '早上好';
  if (hour < 18) return '下午好';
  return '晚上好';
});

// Lifecycle Hooks
onMounted(() => {
  // Initial data load when component is mounted
  // For uni-app, onShow is often preferred for data fetching that needs to refresh when page becomes active
  // loadData(); // Will be called by onShow
  generateDailyTip();
});

onShow(() => {
  // Data load when page is shown or navigated back to
  loadData();
});

// Functions
const generateDailyTip = () => {
  const tips = [
    '每天进步一点点，成就更好的自己！',
    '坚持学习，让知识成为你的力量！',
    '今天的训练，是明天成功的基石！',
    '持续练习，技能才能真正掌握！',
    '每一次学习都是对未来的投资！',
    '专业技能需要刻意练习来精进！',
    '学以致用，让知识转化为能力！',
    '今天比昨天更进步，就是最大的收获！'
  ];
  const today = new Date().toDateString();
  const hash = today.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  const index = Math.abs(hash) % tips.length;
  dailyTip.value = tips[index];
};

const loadData = async () => {
  try {
    // --- Simulate User.me() and User.updateMyUserData() ---
    let currentUser: UserData = {
      id: 'mock_user_id_123',
      full_name: '张三',
      email: '<EMAIL>',
      lingdou_balance: 5,
      points_balance: 0,
      vip_level: '普通',
      total_lingdou_used: 0,
      total_points_earned: 0
    };

    // Simulate initial user data setup if not already done
    if (typeof currentUser.lingdou_balance === 'undefined' || currentUser.lingdou_balance === null) {
      // In a real app, you'd call an API here:
      // await User.updateMyUserData({ lingdou_balance: 5, total_lingdou_used: 0, points_balance: 0, total_points_earned: 0 });
      currentUser.lingdou_balance = 5;
      currentUser.total_lingdou_used = 0;
      currentUser.points_balance = 0;
      currentUser.total_points_earned = 0;
    }
    userStore.setUser(currentUser);

    // --- Simulate API calls for other data ---
    const categoriesData: any[] = [ // Replace with actual Category type
      { id: 'cat1', name: '情商', order: 1 },
      { id: 'cat2', name: '求职招聘', order: 2 },
      { id: 'cat3', name: '思维', order: 3 },
      { id: 'cat4', name: '沟通', order: 4 },
    ];
    categories.value = categoriesData;

    const allTopics: TopicData[] = [
      { id: 'topic1', name: '晋升汇报', order: 1 },
      { id: 'topic2', name: '情商段位来比拼', order: 2 },
      { id: 'topic3', name: '向上管理试炼场', order: 3 },
      // Add more mock topics as needed
    ];

    const userProgressData: UserProgressData[] = [
      { id: 'up1', user_id: currentUser.id, topic_id: 'topic1', knowledge_learned: true, training_count: 2, mastery_level: '熟练', accuracy_rate: 85, best_score: 90 },
      { id: 'up2', user_id: currentUser.id, topic_id: 'topic2', knowledge_learned: false, training_count: 0, mastery_level: '未开始', accuracy_rate: 0, best_score: 0 },
      { id: 'up3', user_id: currentUser.id, topic_id: 'topic3', knowledge_learned: true, training_count: 0, mastery_level: '初学', accuracy_rate: 0, best_score: 0 },
      // Simulate more progress data
    ];

    const allSessions: TrainingSessionData[] = [
      { id: 'ts1', user_id: currentUser.id, topic_id: 'topic1', completion_date: format(new Date(), 'yyyy-MM-dd') + 'T10:00:00Z' },
      { id: 'ts2', user_id: currentUser.id, topic_id: 'topic1', completion_date: format(new Date(), 'yyyy-MM-dd') + 'T11:00:00Z' },
      { id: 'ts3', user_id: currentUser.id, topic_id: 'topic3', completion_date: format(new Date(), 'yyyy-MM-dd') + 'T09:00:00Z' },
      // Simulate sessions for current week/day
      { id: 'ts4', user_id: currentUser.id, topic_id: 'topic1', completion_date: format(new Date(new Date().setDate(new Date().getDate() - 2)), 'yyyy-MM-dd') + 'T14:00:00Z' }, // 2 days ago
      { id: 'ts5', user_id: currentUser.id, topic_id: 'topic2', completion_date: format(new Date(new Date().setDate(new Date().getDate() - 4)), 'yyyy-MM-dd') + 'T16:00:00Z' }, // 4 days ago
    ];

    const questionsData: QuestionData[] = [
      { id: 'q1', type: 'voice', topic_name: '晋升汇报' },
      { id: 'q2', type: 'voice', topic_name: '情商段位来比拼' },
      { id: 'q3', type: 'choice', topic_name: '向上管理试炼场' },
      // Add more mock questions
    ];

    // Identify AI training topics
    aiTrainingTopics.value = new Set(
      questionsData.filter(q => q.type === 'voice').map(q => q.topic_name)
    );

    // Calculate daily goal
    const today = format(new Date(), 'yyyy-MM-dd');
    const todaySessions = allSessions.filter(s => s.completion_date?.startsWith(today)).length;
    dailyGoalCompleted.value = todaySessions;

    // Calculate weekly training
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
    const weekStart = format(startOfWeek, 'yyyy-MM-dd');
    const weekSessions = allSessions.filter(s => s.completion_date && s.completion_date >= weekStart).length;
    weeklyTraining.value = weekSessions;

    // Calculate core statistics
    const totalTraining = userProgressData.reduce((sum, p) => sum + (p.training_count || 0), 0);
    const accuracySum = userProgressData.reduce((sum, p) => sum + (p.accuracy_rate || 0), 0);
    const averageAccuracy = userProgressData.length ? Math.round(accuracySum / userProgressData.length) : 0;
    const masteredTopics = userProgressData.filter(p => p.mastery_level === '精通').length;
    stats.value = { totalTraining, averageAccuracy, masteredTopics };

    // Determine next action
    let action: NextAction | null = null;

    // 1. Find unfinished learning
    const unfinishedLearning = userProgressData.find(p => !p.knowledge_learned);
    if (unfinishedLearning) {
      const topic = allTopics.find(t => t.id === unfinishedLearning.topic_id);
      if (topic) {
        action = { type: 'learn', topicId: topic.id, title: '继续学习', subtitle: topic.name, buttonText: '学习', icon: 'BookOpen', url: `/pages/TopicDetail/index?id=${topic.id}` };
      }
    }

    // 2. If no unfinished learning, find learned but untraind topics
    if (!action) {
      const readyForTraining = userProgressData.find(p => p.knowledge_learned && (p.training_count || 0) === 0);
      if (readyForTraining) {
        const topic = allTopics.find(t => t.id === readyForTraining.topic_id);
        if (topic) {
          action = { type: 'train', topicId: topic.id, title: '开始训练', subtitle: topic.name, buttonText: '开始', icon: 'Play', url: `/pages/Training/index?topic=${topic.id}` };
        }
      }
    }

    // 3. If no new training, recommend continuing the latest trained topic
    if (!action && userProgressData.length > 0) {
      // Sort progress by last_trained_date or updated_date to find the latest
      const latestProgress = [...userProgressData].sort((a, b) => {
        const dateA = a.last_trained_date ? new Date(a.last_trained_date).getTime() : 0;
        const dateB = b.last_trained_date ? new Date(b.last_trained_date).getTime() : 0;
        return dateB - dateA;
      })[0];

      const topic = allTopics.find(t => t.id === latestProgress.topic_id);
      if (topic) {
        action = { type: 'train', topicId: topic.id, title: '继续训练', subtitle: topic.name, buttonText: '开始', icon: 'Play', url: `/pages/Training/index?topic=${topic.id}` };
      }
    }

    // 4. If completely new user, recommend the first topic
    if (!action) {
      const firstTopic = [...allTopics].sort((a, b) => (a.order || 99) - (b.order || 99))[0];
      if (firstTopic) {
        action = { type: 'learn', topicId: firstTopic.id, title: '开启职场成长之路', subtitle: firstTopic.name, buttonText: '学习', icon: 'BookOpen', url: `/pages/TopicDetail/index?id=${firstTopic.id}` };
      }
    }
    nextAction.value = action;

  } catch (error) {
    console.error("用户未登录或数据加载失败", error);
    // Handle error, e.g., redirect to login or show error message
  }
};
</script>

<script lang="ts">
// This part is for registering components if not using <script setup> directly for imports
// In Vue 3 with <script setup>, direct imports are usually enough.
// However, if Icon.vue is in a separate file and needs to be explicitly registered for template usage,
// this block might be needed depending on build setup.
import Icon from '@/components/Icon.vue';

export default {
  components: {
    Icon,
  },
};
</script>

<style scoped>
/* 基础容器样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 50%, #f3e8ff 100%);
}

/* 头部面板样式 */
.header-panel {
  background: linear-gradient(135deg, #60a5fa 0%, #6366f1 50%, #a855f7 100%);
  padding: 32px 16px 8px 16px;
  position: relative;
  overflow: hidden;
}

.content-wrapper {
  max-width: 448px;
  margin: 0 auto;
}

/* 问候语样式 */
.greeting-section {
  margin-bottom: 6px;
}

.greeting-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.wave-emoji {
  font-size: 20px;
}

.tip-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.5;
}

/* 行动卡片样式 */
.action-card {
  background: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  margin-bottom: 6px;
  border-radius: 6px;
  padding: 8px;
}

.action-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-wrapper {
  width: 24px;
  height: 24px;
  background: #eff6ff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-info {
  flex: 1;
  min-width: 0;
}

.action-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2px;
}

.action-title {
  font-size: 12px;
  font-weight: 500;
  color: #1d4ed8;
}

.action-button {
  background: #2563eb;
  color: white;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 4px;
  text-decoration: none;
}

.action-button:hover {
  background: #1d4ed8;
}

.action-subtitle {
  font-weight: bold;
  color: #1e293b;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 训练目标样式 */
.goal-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(8px);
  border-radius: 6px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.goal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.goal-stats {
  display: flex;
  align-items: center;
  gap: 4px;
}

.goal-text {
  color: white;
  font-weight: 500;
  font-size: 12px;
}

.goal-status {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  width: 32px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.7s ease;
}

.progress-fill.daily {
  background: linear-gradient(90deg, #fb923c 0%, #f97316 100%);
}

.progress-fill.weekly {
  background: linear-gradient(90deg, #60a5fa 0%, #6366f1 100%);
}

/* 主要内容区域 */
.main-content {
  padding: 10px 16px 32px 16px;
  max-width: 448px;
  margin: 0 auto;
}

/* AI训练区块样式 */
.ai-section {
  position: relative;
  margin-bottom: 16px;
}

.ai-card {
  background: white;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.ai-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.ai-title-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
}

.ai-emoji {
  font-size: 20px;
}

.ai-title {
  font-size: 16px;
  font-weight: 900;
  color: #1e293b;
}

.ai-badge {
  background: #3b82f6;
  color: white;
  font-size: 12px;
  font-weight: bold;
  border-radius: 4px;
  padding: 2px 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.ai-badge-text {
  color: white;
  font-size: 12px;
}

.ai-description {
  font-size: 14px;
  color: #475569;
  font-weight: 500;
  margin-bottom: 12px;
}

.ai-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ai-main-item {
  background: linear-gradient(90deg, #3b82f6 0%, #a855f7 100%);
  color: white;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ai-main-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-main-info {
  display: flex;
  flex-direction: column;
}

.ai-main-title {
  font-weight: bold;
  font-size: 14px;
  color: white;
}

.ai-main-subtitle {
  font-size: 12px;
  color: #dbeafe;
}

.ai-hot-badge {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  font-size: 12px;
  border-radius: 4px;
  padding: 2px 10px;
}

.ai-sub-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.ai-sub-item {
  color: white;
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.ai-sub-item-1 {
  background: linear-gradient(90deg, #60a5fa 0%, #a855f7 100%);
}

.ai-sub-item-2 {
  background: linear-gradient(90deg, #a855f7 0%, #6366f1 100%);
}

.ai-sub-title {
  font-weight: bold;
  font-size: 12px;
  color: white;
}

.ai-sub-subtitle {
  font-size: 12px;
  color: #dbeafe;
}

.ai-footer {
  margin-top: 12px;
  text-align: center;
}

.ai-footer-text {
  font-size: 12px;
  color: #64748b;
}

.ai-highlight {
  color: #2563eb;
  font-weight: 600;
}

/* 成长足迹样式 */
.stats-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #1e293b;
  margin-bottom: 8px;
  display: block;
}

.stats-card {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  margin-bottom: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
  text-align: center;
  padding: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-weight: bold;
  color: #1e293b;
  font-size: 16px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
}

.leaderboard-card {
  background: linear-gradient(90deg, #2563eb 0%, #6366f1 100%);
  color: white;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.leaderboard-content {
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.leaderboard-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.leaderboard-text {
  display: flex;
  flex-direction: column;
}

.leaderboard-title {
  font-weight: 500;
  color: white;
  font-size: 14px;
}

.leaderboard-subtitle {
  font-size: 12px;
  color: #dbeafe;
}
</style>