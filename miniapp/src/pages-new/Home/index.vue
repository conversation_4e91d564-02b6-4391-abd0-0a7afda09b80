<template>
  <view class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <!-- 今日行动面板 -->
    <view class="bg-gradient-to-br from-blue-400 via-indigo-400 to-purple-400 pt-8 pb-2 px-4 relative overflow-hidden">
      <view class="max-w-md mx-auto">
        <!-- 问候语 -->
        <view class="mb-1.5">
          <text class="text-white text-lg font-bold mb-1 flex items-center gap-1">
            {{ greeting }} {{ user?.full_name?.split(' ')[0] || '学员' }}
            <text class="text-xl">👋</text>
          </text>
          <text class="text-white/90 text-sm leading-relaxed">{{ dailyTip }}</text>
        </view>

        <!-- 核心行动卡片 -->
        <view v-if="nextAction" class="bg-white shadow-lg mb-1.5 rounded-md p-2">
          <view class="flex items-center gap-2">
            <view class="w-6 h-6 bg-blue-50 rounded-md flex items-center justify-center">
              <Icon :name="nextAction.icon" size="16" color="#3b82f6" />
            </view>
            <view class="flex-1 min-w-0">
              <view class="flex items-center justify-between mb-0.5">
                <text class="text-xs font-medium text-blue-700">{{ nextAction.title }}</text>
                <navigator :url="nextAction.url" class="bg-blue-600 hover:bg-blue-700 px-2 py-0.5 h-auto text-xs rounded-[4px] text-white">
                  {{ nextAction.buttonText }}
                </navigator>
              </view>
              <navigator :url="nextAction.url">
                <text class="font-bold text-slate-800 truncate text-sm">{{ nextAction.subtitle }}</text>
              </navigator>
            </view>
          </view>
        </view>

        <!-- 训练目标 -->
        <view class="bg-white/25 backdrop-blur-sm rounded-md p-2">
          <view class="flex items-center justify-between mb-1">
            <view class="flex items-center gap-1">
              <Icon name="Flame" size="12" color="#fb923c" />
              <text class="text-white font-medium text-xs">今日 {{ dailyGoalCompleted }}/{{ DAILY_GOAL }} · 本周 {{ weeklyTraining }}/{{ WEEKLY_GOAL }}</text>
            </view>
            <text class="text-xs text-white/80">
              {{ dailyGoalCompleted >= DAILY_GOAL ? '✅ 达标' : weeklyTraining >= WEEKLY_GOAL ? '🔥 周达标' : '💪 加油' }}
            </text>
          </view>
          <view class="space-y-1">
            <view class="flex items-center gap-2">
              <text class="text-xs text-white/90 w-8">今日</text>
              <view class="flex-1 h-1 bg-white/40 rounded-full">
                <view class="bg-gradient-to-r from-orange-400 to-orange-500 h-1 rounded-full transition-all duration-700" :style="{ width: Math.min((dailyGoalCompleted / DAILY_GOAL) * 100, 100) + '%' }"></view>
              </view>
            </view>
            <view class="flex items-center gap-2">
              <text class="text-xs text-white/90 w-8">本周</text>
              <view class="flex-1 h-1 bg-white/40 rounded-full">
                <view class="bg-gradient-to-r from-blue-400 to-indigo-400 h-1 rounded-full transition-all duration-700" :style="{ width: Math.min((weeklyTraining / WEEKLY_GOAL) * 100, 100) + '%' }"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="px-4 max-w-md mx-auto pt-2.5 pb-8 space-y-4">
      <!-- AI训练特色区块 -->
      <view class="relative">
        <view class="absolute -top-2 -left-2 w-6 h-6 bg-blue-100 rounded-full opacity-60"></view>
        <view class="absolute -top-1 -right-3 w-4 h-4 bg-purple-100 rounded-full opacity-70"></view>
        <view class="bg-white border-0 shadow-lg relative overflow-hidden rounded-md">
          <view class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-blue-100/50 to-transparent rounded-full -translate-y-10 translate-x-10"></view>
          <view class="p-4 relative">
            <view class="flex items-center gap-2 mb-3">
              <view class="flex items-center gap-1">
                <text class="text-xl">🎯</text>
                <text class="text-base font-black text-slate-800">AI智能训练</text>
              </view>
              <view class="bg-blue-500 text-white text-xs font-bold rounded-[4px] px-2.5 py-0.5">
                <Icon name="Sparkles" size="12" color="white" />
                <text>AI驱动</text>
              </view>
            </view>
            <text class="text-sm text-slate-600 mb-3 font-medium">🚀 AI实时分析你的回答，提供个性化指导</text>
            <view class="grid grid-cols-1 gap-2">
              <navigator url="/pages/TopicDetail/index?id=晋升汇报">
                <view class="bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 rounded-md p-3 flex items-center justify-between">
                  <view class="flex items-center gap-2">
                    <Icon name="BrainCircuit" size="20" color="white" />
                    <view>
                      <text class="font-bold text-sm">晋升汇报AI训练</text>
                      <text class="text-xs text-blue-100">AI分析你的表达能力</text>
                    </view>
                  </view>
                  <view class="text-right">
                    <view class="bg-white/25 text-white text-xs rounded-[4px] px-2.5 py-0.5">🔥 热门</view>
                  </view>
                </view>
              </navigator>
              <view class="grid grid-cols-2 gap-2">
                <navigator url="/pages/TopicDetail/index?id=情商段位来比拼">
                  <view class="bg-gradient-to-r from-blue-400 to-purple-500 text-white hover:shadow-md transform hover:scale-[1.02] transition-all duration-200 rounded-md p-2 text-center">
                    <Icon name="Mic" size="16" color="white" class="mx-auto mb-1" />
                    <text class="font-bold text-xs">情商AI测试</text>
                    <text class="text-xs text-blue-100 mt-1">智能情商评估</text>
                  </view>
                </navigator>
                <navigator url="/pages/TrainingHub/index?tab=ai">
                  <view class="bg-gradient-to-r from-purple-400 to-indigo-500 text-white hover:shadow-md transform hover:scale-[1.02] transition-all duration-200 rounded-md p-2 text-center">
                    <Icon name="Zap" size="16" color="white" class="mx-auto mb-1" />
                    <text class="font-bold text-xs">更多AI训练</text>
                    <text class="text-xs text-purple-100 mt-1">探索全部AI功能</text>
                  </view>
                </navigator>
              </view>
            </view>
            <view class="mt-3 text-center">
              <text class="text-xs text-slate-500"><text class="text-blue-600 font-semibold">AI技术驱动</text> 让训练更智能更有效</text>
            </view>
          </view>
        </view>

        <!-- 我的成长足迹 -->
        <view>
          <text class="text-base font-bold text-slate-800 mb-2">我的成长足迹</text>
          <view class="space-y-3">
            <view class="bg-white shadow-sm border-0 rounded-md">
              <view class="p-3">
                <view class="grid grid-cols-3 gap-2 text-center">
                  <view>
                    <Icon name="TrendingUp" size="20" color="#3b82f6" class="mx-auto mb-1" />
                    <text class="font-bold text-slate-800 text-base">{{ stats.totalTraining }}</text>
                    <text class="text-xs text-slate-500">总训练</text>
                  </view>
                  <view>
                    <Icon name="Target" size="20" color="#3b82f6" class="mx-auto mb-1" />
                    <text class="font-bold text-slate-800 text-base">{{ stats.averageAccuracy }}%</text>
                    <text class="text-xs text-slate-500">平均准确率</text>
                  </view>
                  <view>
                    <Icon name="Award" size="20" color="#3b82f6" class="mx-auto mb-1" />
                    <text class="font-bold text-slate-800 text-base">{{ stats.masteredTopics }}</text>
                    <text class="text-xs text-slate-500">精通主题</text>
                  </view>
                </view>
              </view>
            </view>

            <navigator url="/pages/Leaderboard/index">
              <view class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:shadow-lg transition-shadow border-0 rounded-md">
                <view class="p-3 flex items-center justify-between">
                  <view class="flex items-center gap-3">
                    <Icon name="Trophy" size="20" color="#facc15" />
                    <view>
                      <text class="font-medium">挑战英雄榜</text>
                      <text class="text-xs text-blue-100">看看你的全国排名</text>
                    </view>
                  </view>
                  <Icon name="ChevronRight" size="16" color="#bfdbfe" />
                </view>
              </view>
            </navigator>
          </view>
        </view>

        <!-- 热门主题 -->
        <view class="relative">
          <view class="absolute -top-2 -left-2 w-6 h-6 bg-orange-100 rounded-full opacity-60"></view>
          <view class="absolute -top-1 -right-3 w-4 h-4 bg-red-100 rounded-full opacity-70"></view>
          
          <view class="bg-white border-0 shadow-lg relative overflow-hidden rounded-md">
            <view class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-orange-100/50 to-transparent rounded-full -translate-y-10 translate-x-10"></view>
            
            <view class="p-4 relative">
              <view class="flex items-center gap-2 mb-3">
                <view class="flex items-center gap-1">
                  <text class="text-xl animate-pulse">🔥</text>
                  <text class="text-base font-black text-slate-800">热门主题</text>
                </view>
                <view class="bg-orange-500 text-white text-xs font-bold rounded-[4px] px-2.5 py-0.5">
                  <text>必学</text>
                </view>
              </view>
              
              <text class="text-sm text-slate-600 mb-3 font-medium">
                🔥 精选职场必修课，助你快速进阶
              </text>
              
              <view class="grid grid-cols-1 gap-2">
                <navigator url="/pages/TopicDetail/index?id=晋升汇报">
                  <view class="bg-gradient-to-r from-orange-500 to-red-500 text-white hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 border-0 rounded-md">
                    <view class="p-3 flex items-center justify-between">
                      <view class="flex items-center gap-2">
                        <text class="text-lg">💼</text>
                        <view>
                          <text class="font-bold text-sm">晋升汇报</text>
                          <text class="text-xs text-orange-100">升职必备技能</text>
                        </view>
                      </view>
                      <view class="text-right">
                        <view class="bg-white/25 text-white text-xs rounded-[4px] px-2.5 py-0.5">
                          🔥 HOT
                        </view>
                      </view>
                    </view>
                  </view>
                </navigator>
                
                <view class="grid grid-cols-2 gap-2">
                  <navigator url="/pages/TopicDetail/index?id=情商段位来比拼">
                    <view class="bg-gradient-to-r from-orange-400 to-red-400 text-white hover:shadow-md transform hover:scale-[1.02] transition-all duration-200 border-0 rounded-md">
                      <view class="p-2 text-center">
                        <text class="text-base">🧠</text>
                        <text class="font-bold text-xs mt-1">情商段位来比拼</text>
                        <text class="text-xs text-orange-100 mt-1">测试你的情商</text>
                      </view>
                    </view>
                  </navigator>
                  
                  <navigator url="/pages/TopicDetail/index?id=向上管理试炼场">
                    <view class="bg-gradient-to-r from-red-400 to-pink-400 text-white hover:shadow-md transform hover:scale-[1.02] transition-all duration-200 border-0 rounded-md">
                      <view class="p-2 text-center">
                        <text class="text-base">⚡</text>
                        <text class="font-bold text-xs mt-1">向上管理试炼场</text>
                        <text class="text-xs text-red-100 mt-1">搞定你的老板</text>
                      </view>
                    </view>
                  </navigator>
                </view>
              </view>
              
              <view class="mt-3 text-center">
                <text class="text-xs text-slate-500">
                  <text class="text-orange-600 font-semibold">2.3万人</text> 本周已开始学习
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 技能分类 (探索) -->
        <view>
          <view class="flex items-center justify-between mb-3">
            <text class="text-base font-bold text-slate-800">技能分类</text>
            <navigator url="/pages/Learning/index" class="text-blue-600 text-sm font-medium hover:text-blue-700 transition-colors flex items-center gap-1">
              <text>查看全部</text>
              <Icon name="ChevronRight" size="12" color="#2563eb" />
            </navigator>
          </view>
          
          <view class="grid grid-cols-2 gap-3">
            <navigator url="/pages/Learning/index?tab=情商">
              <view class="hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 active:scale-95 border-0 bg-white group shadow-sm rounded-md">
                <view class="p-3 text-center">
                  <view class="w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                    <text class="text-xl">🧠</text>
                  </view>
                  <text class="font-bold text-slate-800 text-sm mb-1 group-hover:text-blue-600 transition-colors">情商</text>
                  <text class="text-xs text-slate-600 line-clamp-1 mb-2">情绪管理与人际沟通</text>
                  <view class="flex items-center justify-center gap-1 text-xs">
                    <view class="w-1.5 h-1.5 bg-blue-400 rounded-full"></view>
                    <text class="text-slate-500">3个模块</text>
                  </view>
                </view>
              </view>
            </navigator>

            <navigator url="/pages/Learning/index?tab=求职招聘">
              <view class="hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 active:scale-95 border-0 bg-white group shadow-sm rounded-md">
                <view class="p-3 text-center">
                  <view class="w-12 h-12 bg-gradient-to-br from-orange-100 to-red-100 rounded-2xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                    <text class="text-xl">💼</text>
                  </view>
                  <text class="font-bold text-slate-800 text-sm mb-1 group-hover:text-blue-600 transition-colors">求职招聘</text>
                  <text class="text-xs text-slate-600 line-clamp-1 mb-2">面试技巧与人才选拔</text>
                  <view class="flex items-center justify-center gap-1 text-xs">
                    <view class="w-1.5 h-1.5 bg-blue-400 rounded-full"></view>
                    <text class="text-slate-500">2个模块</text>
                  </view>
                </view>
              </view>
            </navigator>

            <navigator url="/pages/Learning/index?tab=思维">
              <view class="hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 active:scale-95 border-0 bg-white group shadow-sm rounded-md">
                <view class="p-3 text-center">
                  <view class="w-12 h-12 bg-gradient-to-br from-green-100 to-teal-100 rounded-2xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                    <text class="text-xl">🧩</text>
                  </view>
                  <text class="font-bold text-slate-800 text-sm mb-1 group-hover:text-blue-600 transition-colors">思维</text>
                  <text class="text-xs text-slate-600 line-clamp-1 mb-2">逻辑思维与问题解决</text>
                  <view class="flex items-center justify-center gap-1 text-xs">
                    <view class="w-1.5 h-1.5 bg-blue-400 rounded-full"></view>
                    <text class="text-slate-500">3个模块</text>
                  </view>
                </view>
              </view>
            </navigator>

            <navigator url="/pages/Learning/index?tab=沟通">
              <view class="hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 active:scale-95 border-0 bg-white group shadow-sm rounded-md">
                <view class="p-3 text-center">
                  <view class="w-12 h-12 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                    <text class="text-xl">💬</text>
                  </view>
                  <text class="font-bold text-slate-800 text-sm mb-1 group-hover:text-blue-600 transition-colors">沟通</text>
                  <text class="text-xs text-slate-600 line-clamp-1 mb-2">团队协作与客户服务</text>
                  <view class="flex items-center justify-center gap-1 text-xs">
                    <view class="w-1.5 h-1.5 bg-blue-400 rounded-full"></view>
                    <text class="text-slate-500">3个模块</text>
                  </view>
                </view>
              </view>
            </navigator>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
// import { useRouter } from 'vue-router'; // For web, uni-app uses uni.navigateTo
import { onShow } from '@dcloudio/uni-app'; // uni-app specific lifecycle hook
import { useUserStore } from '@/stores/user';

// Assuming these are your API entities or mock data structures
// import { Category, UserProgress, User, TrainingSession, Topic, Question } from '@/api/entities';
import { format } from 'date-fns';

// Define interfaces for better type safety
interface UserData {
  id: string;
  full_name?: string;
  email?: string;
  lingdou_balance?: number;
  points_balance?: number;
  vip_level?: string;
  total_lingdou_used?: number;
  total_points_earned?: number;
}

interface UserProgressData {
  id: string;
  user_id: string;
  topic_id: string;
  knowledge_learned?: boolean;
  mastery_level?: string;
  training_count?: number;
  accuracy_rate?: number;
  best_score?: number;
  correct_answers?: number;
  total_questions?: number;
  last_trained_date?: string;
}

interface TopicData {
  id: string;
  name: string;
  order?: number;
  // Add other topic properties as needed
}

interface TrainingSessionData {
  id: string;
  user_id: string;
  topic_id: string;
  completion_date?: string;
  // Add other session properties as needed
}

interface QuestionData {
  id: string;
  type: 'choice' | 'voice';
  topic_name: string;
  // Add other question properties as needed
}

interface NextAction {
  type: 'learn' | 'train';
  topicId: string;
  title: string;
  subtitle: string;
  buttonText: string;
  icon: string; // Changed to string to match Icon component prop
  url: string;
}

// Constants
const DAILY_GOAL = 1;
const WEEKLY_GOAL = 5;

// Reactive State
const categories = ref<any[]>([]); // Replace any[] with actual Category type if available
const userStore = useUserStore();
const dailyGoalCompleted = ref(0);
const weeklyTraining = ref(0);
const stats = ref({ totalTraining: 0, averageAccuracy: 0, masteredTopics: 0 });
const nextAction = ref<NextAction | null>(null);
const dailyTip = ref('');
const aiTrainingTopics = ref<Set<string>>(new Set());

// Computed property to get user from store for the template
const user = computed(() => userStore.user);

// Computed Properties
const greeting = computed(() => {
  const hour = new Date().getHours();
  if (hour < 12) return '早上好';
  if (hour < 18) return '下午好';
  return '晚上好';
});

// Lifecycle Hooks
onMounted(() => {
  // Initial data load when component is mounted
  // For uni-app, onShow is often preferred for data fetching that needs to refresh when page becomes active
  // loadData(); // Will be called by onShow
  generateDailyTip();
});

onShow(() => {
  // Data load when page is shown or navigated back to
  loadData();
});

// Functions
const generateDailyTip = () => {
  const tips = [
    '每天进步一点点，成就更好的自己！',
    '坚持学习，让知识成为你的力量！',
    '今天的训练，是明天成功的基石！',
    '持续练习，技能才能真正掌握！',
    '每一次学习都是对未来的投资！',
    '专业技能需要刻意练习来精进！',
    '学以致用，让知识转化为能力！',
    '今天比昨天更进步，就是最大的收获！'
  ];
  const today = new Date().toDateString();
  const hash = today.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  const index = Math.abs(hash) % tips.length;
  dailyTip.value = tips[index];
};

const loadData = async () => {
  try {
    // --- Simulate User.me() and User.updateMyUserData() ---
    let currentUser: UserData = {
      id: 'mock_user_id_123',
      full_name: '张三',
      email: '<EMAIL>',
      lingdou_balance: 5,
      points_balance: 0,
      vip_level: '普通',
      total_lingdou_used: 0,
      total_points_earned: 0
    };

    // Simulate initial user data setup if not already done
    if (typeof currentUser.lingdou_balance === 'undefined' || currentUser.lingdou_balance === null) {
      // In a real app, you'd call an API here:
      // await User.updateMyUserData({ lingdou_balance: 5, total_lingdou_used: 0, points_balance: 0, total_points_earned: 0 });
      currentUser.lingdou_balance = 5;
      currentUser.total_lingdou_used = 0;
      currentUser.points_balance = 0;
      currentUser.total_points_earned = 0;
    }
    userStore.setUser(currentUser);

    // --- Simulate API calls for other data ---
    const categoriesData: any[] = [ // Replace with actual Category type
      { id: 'cat1', name: '情商', order: 1 },
      { id: 'cat2', name: '求职招聘', order: 2 },
      { id: 'cat3', name: '思维', order: 3 },
      { id: 'cat4', name: '沟通', order: 4 },
    ];
    categories.value = categoriesData;

    const allTopics: TopicData[] = [
      { id: 'topic1', name: '晋升汇报', order: 1 },
      { id: 'topic2', name: '情商段位来比拼', order: 2 },
      { id: 'topic3', name: '向上管理试炼场', order: 3 },
      // Add more mock topics as needed
    ];

    const userProgressData: UserProgressData[] = [
      { id: 'up1', user_id: currentUser.id, topic_id: 'topic1', knowledge_learned: true, training_count: 2, mastery_level: '熟练', accuracy_rate: 85, best_score: 90 },
      { id: 'up2', user_id: currentUser.id, topic_id: 'topic2', knowledge_learned: false, training_count: 0, mastery_level: '未开始', accuracy_rate: 0, best_score: 0 },
      { id: 'up3', user_id: currentUser.id, topic_id: 'topic3', knowledge_learned: true, training_count: 0, mastery_level: '初学', accuracy_rate: 0, best_score: 0 },
      // Simulate more progress data
    ];

    const allSessions: TrainingSessionData[] = [
      { id: 'ts1', user_id: currentUser.id, topic_id: 'topic1', completion_date: format(new Date(), 'yyyy-MM-dd') + 'T10:00:00Z' },
      { id: 'ts2', user_id: currentUser.id, topic_id: 'topic1', completion_date: format(new Date(), 'yyyy-MM-dd') + 'T11:00:00Z' },
      { id: 'ts3', user_id: currentUser.id, topic_id: 'topic3', completion_date: format(new Date(), 'yyyy-MM-dd') + 'T09:00:00Z' },
      // Simulate sessions for current week/day
      { id: 'ts4', user_id: currentUser.id, topic_id: 'topic1', completion_date: format(new Date(new Date().setDate(new Date().getDate() - 2)), 'yyyy-MM-dd') + 'T14:00:00Z' }, // 2 days ago
      { id: 'ts5', user_id: currentUser.id, topic_id: 'topic2', completion_date: format(new Date(new Date().setDate(new Date().getDate() - 4)), 'yyyy-MM-dd') + 'T16:00:00Z' }, // 4 days ago
    ];

    const questionsData: QuestionData[] = [
      { id: 'q1', type: 'voice', topic_name: '晋升汇报' },
      { id: 'q2', type: 'voice', topic_name: '情商段位来比拼' },
      { id: 'q3', type: 'choice', topic_name: '向上管理试炼场' },
      // Add more mock questions
    ];

    // Identify AI training topics
    aiTrainingTopics.value = new Set(
      questionsData.filter(q => q.type === 'voice').map(q => q.topic_name)
    );

    // Calculate daily goal
    const today = format(new Date(), 'yyyy-MM-dd');
    const todaySessions = allSessions.filter(s => s.completion_date?.startsWith(today)).length;
    dailyGoalCompleted.value = todaySessions;

    // Calculate weekly training
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
    const weekStart = format(startOfWeek, 'yyyy-MM-dd');
    const weekSessions = allSessions.filter(s => s.completion_date && s.completion_date >= weekStart).length;
    weeklyTraining.value = weekSessions;

    // Calculate core statistics
    const totalTraining = userProgressData.reduce((sum, p) => sum + (p.training_count || 0), 0);
    const accuracySum = userProgressData.reduce((sum, p) => sum + (p.accuracy_rate || 0), 0);
    const averageAccuracy = userProgressData.length ? Math.round(accuracySum / userProgressData.length) : 0;
    const masteredTopics = userProgressData.filter(p => p.mastery_level === '精通').length;
    stats.value = { totalTraining, averageAccuracy, masteredTopics };

    // Determine next action
    let action: NextAction | null = null;

    // 1. Find unfinished learning
    const unfinishedLearning = userProgressData.find(p => !p.knowledge_learned);
    if (unfinishedLearning) {
      const topic = allTopics.find(t => t.id === unfinishedLearning.topic_id);
      if (topic) {
        action = { type: 'learn', topicId: topic.id, title: '继续学习', subtitle: topic.name, buttonText: '学习', icon: 'BookOpen', url: `/pages/TopicDetail/index?id=${topic.id}` };
      }
    }

    // 2. If no unfinished learning, find learned but untraind topics
    if (!action) {
      const readyForTraining = userProgressData.find(p => p.knowledge_learned && (p.training_count || 0) === 0);
      if (readyForTraining) {
        const topic = allTopics.find(t => t.id === readyForTraining.topic_id);
        if (topic) {
          action = { type: 'train', topicId: topic.id, title: '开始训练', subtitle: topic.name, buttonText: '开始', icon: 'Play', url: `/pages/Training/index?topic=${topic.id}` };
        }
      }
    }

    // 3. If no new training, recommend continuing the latest trained topic
    if (!action && userProgressData.length > 0) {
      // Sort progress by last_trained_date or updated_date to find the latest
      const latestProgress = [...userProgressData].sort((a, b) => {
        const dateA = a.last_trained_date ? new Date(a.last_trained_date).getTime() : 0;
        const dateB = b.last_trained_date ? new Date(b.last_trained_date).getTime() : 0;
        return dateB - dateA;
      })[0];

      const topic = allTopics.find(t => t.id === latestProgress.topic_id);
      if (topic) {
        action = { type: 'train', topicId: topic.id, title: '继续训练', subtitle: topic.name, buttonText: '开始', icon: 'Play', url: `/pages/Training/index?topic=${topic.id}` };
      }
    }

    // 4. If completely new user, recommend the first topic
    if (!action) {
      const firstTopic = [...allTopics].sort((a, b) => (a.order || 99) - (b.order || 99))[0];
      if (firstTopic) {
        action = { type: 'learn', topicId: firstTopic.id, title: '开启职场成长之路', subtitle: firstTopic.name, buttonText: '学习', icon: 'BookOpen', url: `/pages/TopicDetail/index?id=${firstTopic.id}` };
      }
    }
    nextAction.value = action;

  } catch (error) {
    console.error("用户未登录或数据加载失败", error);
    // Handle error, e.g., redirect to login or show error message
  }
};
</script>

<script lang="ts">
// This part is for registering components if not using <script setup> directly for imports
// In Vue 3 with <script setup>, direct imports are usually enough.
// However, if Icon.vue is in a separate file and needs to be explicitly registered for template usage,
// this block might be needed depending on build setup.
import Icon from '@/components/Icon.vue';

export default {
  components: {
    Icon,
  },
};
</script>

<style scoped>
/* Tailwind CSS classes are assumed to be processed by uni-app's build setup */
.min-h-screen {
  min-height: 100vh;
}

.max-w-md {
  max-width: 768px; /* Adjusted from original 448px for better mobile layout, but keeping original for now */
}

/* Specific gradients if not fully covered by Tailwind */
.bg-gradient-to-br {
  /* Example: background-image: linear-gradient(to bottom right, #f0f9ff, #e0f2fe, #dbeafe); */
}
</style>