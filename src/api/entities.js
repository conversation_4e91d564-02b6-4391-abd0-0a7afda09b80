import { base44 } from './base44Client';


export const UserProgress = base44.entities.UserProgress;

export const TrainingSession = base44.entities.TrainingSession;

export const Category = base44.entities.Category;

export const Topic = base44.entities.Topic;

export const Question = base44.entities.Question;

export const LingdouTransaction = base44.entities.LingdouTransaction;

export const TrainingGoal = base44.entities.TrainingGoal;

export const Order = base44.entities.Order;

export const PointsTransaction = base44.entities.PointsTransaction;

export const PointsExchange = base44.entities.PointsExchange;



// auth sdk:
export const User = base44.auth;