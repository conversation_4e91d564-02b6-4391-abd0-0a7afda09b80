
import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Question, Topic, UserProgress, TrainingSession, User, LingdouTransaction, PointsTransaction } from '@/api/entities';
import { InvokeLLM } from '@/api/integrations';
import { ArrowLeft, Mic, Send, Volume2, Check, X, Loader2, ThumbsUp, ThumbsDown, Gem, Sparkles, Zap, Award, Gift, ChevronLeft, ChevronRight, Target, BrainCircuit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { motion, AnimatePresence } from 'framer-motion';
import { ResponsiveContainer, Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Legend } from 'recharts';
import { createPageUrl } from '@/utils';

// AI反馈组件
const AIFeedback = ({ feedback, userAnswer, inputMode }) => {
  if (!feedback) return null;

  const radarData = [
    { subject: '问题理解', A: feedback.problem_understanding || 0, fullMark: 5 },
    { subject: '原则体现', A: feedback.principle_application || 0, fullMark: 5 },
    { subject: '知识点掌握', A: feedback.knowledge_mastery || 0, fullMark: 5 },
    { subject: '语言逻辑性', A: feedback.logical_coherence || 0, fullMark: 5 },
    { subject: '语言流畅度', A: feedback.fluency || 0, fullMark: 5 },
  ];

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="space-y-6 text-center">
      <div className="space-y-2">
        <h3 className={`text-4xl font-bold ${feedback.passed ? 'text-green-500' : 'text-red-500'}`}>
          {feedback.passed ? '通过' : '待提升'}
        </h3>
        <p className="text-slate-600 text-lg">"{feedback.comment}"</p>
      </div>

      <div className="h-60 w-full">
        <ResponsiveContainer>
          <RadarChart cx="50%" cy="50%" outerRadius="80%" data={radarData}>
            <PolarGrid stroke="#e2e8f0" />
            <PolarAngleAxis dataKey="subject" tick={{ fill: '#475569', fontSize: 12 }} />
            <PolarRadiusAxis angle={30} domain={[0, 5]} tick={false} axisLine={false} />
            <Radar name="得分" dataKey="A" stroke="#1e40af" fill="#3b82f6" fillOpacity={0.6} />
          </RadarChart>
        </ResponsiveContainer>
      </div>

      {/* 用户原始答案 - 弱化显示 */}
      {userAnswer && (
        <div className="bg-slate-50 border border-slate-200 rounded-lg p-3 text-left">
          <div className="flex items-center gap-2 mb-2">
            {inputMode === 'voice' ? (
              <Mic className="w-4 h-4 text-slate-400" />
            ) : (
              <BrainCircuit className="w-4 h-4 text-slate-400" />
            )}
            <span className="text-xs font-medium text-slate-500">
              您的{inputMode === 'voice' ? '语音' : '文字'}回答
            </span>
          </div>
          <p className="text-sm text-slate-600 leading-relaxed bg-white p-2 rounded border border-slate-100">
            {userAnswer}
          </p>
        </div>
      )}

      <div className="text-left space-y-4">
        <div>
          <h4 className="font-semibold flex items-center gap-2 mb-2 text-green-600"><ThumbsUp className="w-5 h-5"/>亮点</h4>
          <div className="flex items-start gap-3 p-4 bg-green-50 rounded-xl border border-green-200">
            <p className="text-sm text-green-800">{feedback.strength}</p>
          </div>
        </div>
        <div>
          <h4 className="font-semibold flex items-center gap-2 mb-2 text-red-600"><ThumbsDown className="w-5 h-5"/>可改进</h4>
          <div className="flex items-start gap-3 p-4 bg-red-50 rounded-xl border border-red-200">
            <p className="text-sm text-red-800">{feedback.weakness}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default function Training() {
  const navigate = useNavigate();
  const location = useLocation();

  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [selectedOption, setSelectedOption] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isProcessingAI, setIsProcessingAI] = useState(false);
  const [aiFeedback, setAIFeedback] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [sessionFinished, setSessionFinished] = useState(false);
  const [finalScore, setFinalScore] = useState(0);
  const [user, setUser] = useState(null);
  const [aiInspiration, setAiInspiration] = useState('');
  const [isLoadingInspiration, setIsLoadingInspiration] = useState(false);
  const [currentTopic, setCurrentTopic] = useState(null);
  const [inputMode, setInputMode] = useState('voice'); // 'voice' 或 'text'

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const currentUser = await User.me();
      setUser(currentUser);

      const urlParams = new URLSearchParams(location.search);
      let topicId = urlParams.get('topic') || urlParams.get('id');

      console.log("Training page - URL params:", urlParams.toString());
      console.log("Training page - topicId:", topicId);

      if (!topicId) {
        console.error("No topic ID found in URL");
        setQuestions([]);
        setIsLoading(false);
        return;
      }

      // 先获取所有主题，找到对应的主题
      const allTopics = await Topic.list();
      let foundTopic = allTopics.find(t => t.id === topicId);
      
      if (!foundTopic) {
        // 如果通过ID找不到，尝试通过名称查找
        foundTopic = allTopics.find(t => t.name === topicId);
      }
      
      let topicNameForQuestions = null;

      if (foundTopic) {
        topicNameForQuestions = foundTopic.name;
        setCurrentTopic(foundTopic);
      } else if (topicId && topicId.includes('default_topic_')) {
        // 处理从前端传递过来的、数据库中不存在的“模拟主题”ID
        console.log("Mock topic ID detected in Training page:", topicId);
        
        // 从可能格式不正确的ID中提取出干净的主题名称
        const lastPrefixIndex = topicId.lastIndexOf('default_topic_');
        const cleanIdPart = topicId.substring(lastPrefixIndex + 'default_topic_'.length);
        const nameParts = cleanIdPart.split('_');
        
        let extractedName;
        // 假设ID格式为：default_topic_分类_模块_主题名
        if (nameParts.length >= 3) { 
            extractedName = nameParts.slice(2).join('_');
        } else { // 兼容简单格式：default_topic_主题名
            extractedName = nameParts.join('_');
        }
        
        console.log("Extracted mock topic name:", extractedName);
        topicNameForQuestions = extractedName;
        // 创建一个临时的 topic 对象用于页面显示
        setCurrentTopic({ name: extractedName, id: topicId });
      }


      if (!topicNameForQuestions) {
        console.error("Topic not found and could not determine topic name:", topicId);
        setQuestions([]);
        setIsLoading(false);
        return;
      }

      // 使用提取出的 topicNameForQuestions 来获取问题
      const allQuestions = await Question.list();
      const topicQuestions = allQuestions.filter(q => q.topic_name === topicNameForQuestions);

      console.log("All questions:", allQuestions.length);
      console.log("Topic questions for", topicNameForQuestions, ":", topicQuestions.length);

      setQuestions(topicQuestions);
    } catch (error) {
      console.error("加载数据失败:", error);
      setQuestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const calculatePoints = (isCorrect, difficulty = 1) => {
    // 基础积分：答对10分，答错5分
    let basePoints = isCorrect ? 10 : 5;
    // 难度系数：1-5对应1.0-1.5倍
    let difficultyMultiplier = 1 + (difficulty - 1) * 0.1;
    return Math.round(basePoints * difficultyMultiplier);
  };

  const currentQuestion = questions[currentQuestionIndex];

  const handleOptionSelect = (option) => {
    if (showResult) return;
    setSelectedOption(option);
    
    // 更新答案数组，如果当前题目已有答案则替换
    const newAnswers = [...answers];
    const existingAnswerIndex = newAnswers.findIndex(a => a.question_id === currentQuestion.id);
    const newAnswer = { question_id: currentQuestion.id, is_correct: option.is_correct, selected_option_label: option.label }; // Store selected option label
    
    if (existingAnswerIndex >= 0) {
      newAnswers[existingAnswerIndex] = newAnswer;
    } else {
      newAnswers.push(newAnswer);
    }
    
    setAnswers(newAnswers);
    setShowResult(true);
  };

  // 重置当前题目状态
  const resetQuestionState = () => {
    setShowResult(false);
    setSelectedOption(null);
    setAIFeedback(null);
    setTranscript('');
    setAiInspiration('');
  };

  // 恢复指定题目的状态
  const restoreQuestionState = (questionIndex) => {
    const question = questions[questionIndex];
    if (!question) return;

    const existingAnswer = answers.find(a => a.question_id === question.id);
    
    if (existingAnswer) {
      setShowResult(true);
      
      if (question.type === 'choice') {
        // 恢复选择题的选中状态，优先使用存储的label
        const selectedLabel = existingAnswer.selected_option_label;
        if (selectedLabel) {
          setSelectedOption(question.options.find(opt => opt.label === selectedLabel));
        } else {
          // Fallback if label wasn't stored (older answers or problem in data)
          const correctOption = question.options?.find(opt => opt.is_correct);
          setSelectedOption(existingAnswer.is_correct ? correctOption : question.options?.find(opt => !opt.is_correct));
        }
      } else if (question.type === 'voice') {
        // 对于语音题，恢复用户的原始答案和输入方式
        if (existingAnswer.user_answer) {
          setTranscript(existingAnswer.user_answer);
          setInputMode(existingAnswer.input_mode || 'voice');
        }
        
        // 显示已回答状态的简化反馈
        setAIFeedback({
          passed: existingAnswer.is_correct,
          comment: "已回答完成",
          strength: "点击“重新回答”可以再次作答",
          weakness: "或点击“下一题”继续",
          problem_understanding: 0,
          principle_application: 0,
          knowledge_mastery: 0,
          logical_coherence: 0,
          fluency: 0
        });
      }
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      resetQuestionState();
      restoreQuestionState(currentQuestionIndex + 1);
    } else {
      finishSession();
    }
  };
  
  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      resetQuestionState();
      restoreQuestionState(currentQuestionIndex - 1);
    }
  };

  const handleRetryQuestion = () => {
    resetQuestionState();
  };

  const handleGetInspiration = async () => {
    try {
      const currentUser = await User.me(); // Ensure we have the latest user data
      
      // 检查用户是否有足够的灵豆
      if ((currentUser.lingdou_balance || 0) < 1) {
        const confirmBuy = window.confirm('💎 灵豆不足！\n\n获取AI灵感需要1个灵豆，这将帮助你构思更优质的回答。\n\n是否前往购买灵豆？');
        if (confirmBuy) {
          navigate(createPageUrl('Membership'));
        }
        return;
      }

      setIsLoadingInspiration(true);
      setAiInspiration('');
      
      const prompt = `针对问题："${currentQuestion.content}"，请提供三个核心的回答思路或要点，帮助我构思答案。请直接返回思路要点，无需额外解释。`;
      
      try {
        const result = await InvokeLLM({ prompt });
        setAiInspiration(result);
        
        // 扣除1个灵豆
        const newBalance = (currentUser.lingdou_balance || 0) - 1;
        await User.updateMyUserData({
          lingdou_balance: newBalance,
          total_lingdou_used: (currentUser.total_lingdou_used || 0) + 1
        });
        
        // 记录交易记录
        await LingdouTransaction.create({
          user_id: currentUser.id,
          type: 'consume',
          amount: -1,
          reason: '获取AI回答灵感',
          balance_after: newBalance
        });
        
        // 更新用户状态
        setUser({ ...currentUser, lingdou_balance: newBalance });
        
      } catch (error) {
        console.error("AI inspiration error:", error);
        setAiInspiration("获取灵感失败，请稍后再试。");
      }
    } catch (error) {
      console.error("用户数据获取失败", error);
      setAiInspiration("无法获取用户灵豆信息，请检查网络。");
    } finally {
      setIsLoadingInspiration(false);
    }
  };

  const handleVoiceSubmit = async () => {
    setIsProcessingAI(true);
    const prompt = `
      模拟职场面试官，评估以下回答。
      问题是：'${currentQuestion.content}'
      用户的回答是：'${transcript}'
      核心考察知识点：'${currentQuestion.analysis}'

      请根据以下JSON格式返回评估结果，所有评分项为1-5分：
      {
        "passed": boolean, // 综合判断是否通过
        "comment": "string", // 一句总结性评语
        "strength": "string", // 找到一个最主要的亮点
        "weakness": "string", // 找到一个最需要改进的点
        "problem_understanding": number,
        "principle_application": number,
        "knowledge_mastery": number,
        "logical_coherence": number,
        "fluency": number
      }
    `;

    try {
      const result = await InvokeLLM({ prompt, response_json_schema: { type: "object" } });
      setAIFeedback(result);
      
      // 更新答案数组，同时保存用户的原始回答
      const newAnswers = [...answers];
      const existingAnswerIndex = newAnswers.findIndex(a => a.question_id === currentQuestion.id);
      const newAnswer = { 
        question_id: currentQuestion.id, 
        is_correct: result.passed,
        user_answer: transcript, // 保存用户原始答案
        input_mode: inputMode // 保存输入方式
      };
      
      if (existingAnswerIndex >= 0) {
        newAnswers[existingAnswerIndex] = newAnswer;
      } else {
        newAnswers.push(newAnswer);
      }
      
      setAnswers(newAnswers);
    } catch (error) {
      console.error("AI feedback error:", error);

      let errorMessage = "评估出错，请稍后重试。";
      // Check if the error is due to too many requests
      if (error.isAxiosError && error.response && error.response.status === 429) {
        errorMessage = "AI分析服务器正忙，请稍等片刻再试。";
      }

      // Provide a fallback feedback message
      setAIFeedback({
        passed: false,
        comment: errorMessage,
        strength: "本次无法进行评估。",
        weakness: "请检查您的网络连接或稍后重试。",
        problem_understanding: 0,
        principle_application: 0,
        knowledge_mastery: 0,
        logical_coherence: 0,
        fluency: 0
      });
    } finally {
      setIsProcessingAI(false);
      setShowResult(true);
    }
  };

  const finishSession = async () => {
    const correctCount = answers.filter(a => a.is_correct).length;
    const score = Math.round((correctCount / questions.length) * 100);
    setFinalScore(score);
    
    // Calculate points for this training session
    let totalPoints = 0;
    answers.forEach((answer) => { // Iterate through answers, not questions by index directly
      const question = questions.find(q => q.id === answer.question_id);
      const points = calculatePoints(answer.is_correct, question?.difficulty || 1);
      totalPoints += points;
    });

    // Reward bonus points based on accuracy
    let bonusPoints = 0;
    if (score >= 90) bonusPoints = 20;
    else if (score >= 80) bonusPoints = 15;
    else if (score >= 70) bonusPoints = 10;
    else if (score >= 60) bonusPoints = 5;

    const finalTotalPoints = totalPoints + bonusPoints;

    try {
      if (!user || !currentTopic) {
        console.error("User or topic not loaded, cannot save user progress.");
        return;
      }

      // Update user points
      const newPointsBalance = (user.points_balance || 0) + finalTotalPoints;
      const newTotalEarned = (user.total_points_earned || 0) + finalTotalPoints;
      
      await User.updateMyUserData({
        points_balance: newPointsBalance,
        total_points_earned: newTotalEarned
      });

      // Record transaction
      await PointsTransaction.create({
        user_id: user.id,
        type: 'earn',
        amount: finalTotalPoints,
        reason: `${currentTopic.name}训练完成 (${score}分)`,
        balance_after: newPointsBalance
      });

      const progressRecords = await UserProgress.filter({ user_id: user.id, topic_id: currentTopic.id });

      const existingProgress = progressRecords.length > 0 ? progressRecords[0] : null;

      const newTrainingCount = (existingProgress?.training_count || 0) + 1;
      const totalQuestionsAnswered = (existingProgress?.total_questions || 0) + questions.length;
      const totalCorrectAnswers = (existingProgress?.correct_answers || 0) + correctCount;
      const newAccuracy = totalQuestionsAnswered > 0 ? (totalCorrectAnswers / totalQuestionsAnswered) : 0;

      if (existingProgress) {
        await UserProgress.update(existingProgress.id, {
          training_count: newTrainingCount,
          correct_answers: totalCorrectAnswers,
          total_questions: totalQuestionsAnswered,
          accuracy_rate: Math.round(newAccuracy * 100),
          best_score: Math.max(existingProgress.best_score || 0, score),
          last_trained_date: new Date().toISOString().split('T')[0],
        });
      } else {
        await UserProgress.create({
          user_id: user.id,
          topic_id: currentTopic.id,
          training_count: 1,
          correct_answers: correctCount,
          total_questions: questions.length,
          accuracy_rate: score,
          best_score: score,
          last_trained_date: new Date().toISOString().split('T')[0],
        });
      }

      await TrainingSession.create({
        user_id: user.id,
        topic_id: currentTopic.id,
        questions_answered: questions.length,
        correct_count: correctCount,
        total_score: score,
        completion_date: new Date().toISOString(),
      });
      setSessionFinished(true);
    } catch (error) {
      console.error("保存训练记录失败:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <Loader2 className="animate-spin w-8 h-8 mx-auto mb-4" />
          <p className="text-slate-600">加载训练题目...</p>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-screen p-6 text-center">
        <h3 className="text-lg font-semibold text-slate-700 mb-2">暂无训练内容</h3>
        <p className="text-sm text-slate-500 mb-2">当前主题: {currentTopic?.name || '未知'}</p>
        <p className="text-sm text-slate-500 mb-6">该主题下还没有可用的训练题目。</p>
        <div className="space-y-3">
          <Button onClick={() => navigate(createPageUrl('TrainingHub'))}>
            返回训练中心
          </Button>
          {currentTopic && (
            <Button variant="outline" onClick={() => navigate(createPageUrl(`TopicDetail?id=${currentTopic.id}`))}>
              查看主题详情
            </Button>
          )}
        </div>
      </div>
    );
  }

  if (sessionFinished) {
    const correctCount = answers.filter(a => a.is_correct).length;
    let totalPoints = 0;
    answers.forEach((answer) => { // Iterate through answers, not questions by index directly
      const question = questions.find(q => q.id === answer.question_id);
      const points = calculatePoints(answer.is_correct, question?.difficulty || 1);
      totalPoints += points;
    });
    
    let bonusPoints = 0;
    if (finalScore >= 90) bonusPoints = 20;
    else if (finalScore >= 80) bonusPoints = 15;
    else if (finalScore >= 70) bonusPoints = 10;
    else if (finalScore >= 60) bonusPoints = 5;

    const finalTotalPoints = totalPoints + bonusPoints;

    // Determine level and color based on score
    const getScoreLevel = (score) => {
      if (score >= 90) return { level: '优秀', color: 'from-green-500 to-emerald-600', emoji: '🎉' };
      if (score >= 80) return { level: '良好', color: 'from-blue-500 to-indigo-600', emoji: '👍' };
      if (score >= 70) return { level: '及格', color: 'from-yellow-500 to-orange-500', emoji: '💪' };
      return { level: '需提升', color: 'from-red-500 to-pink-600', emoji: '🔥' };
    };

    const scoreLevel = getScoreLevel(finalScore);

    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex flex-col items-center justify-center p-4 sm:p-6">
        <motion.div 
          initial={{ scale: 0.8, opacity: 0 }} 
          animate={{ scale: 1, opacity: 1 }} 
          transition={{ duration: 0.5 }}
          className="w-full max-w-sm bg-white rounded-2xl shadow-2xl overflow-hidden"
        >
          {/* Header Area */}
          <div className={`bg-gradient-to-r ${scoreLevel.color} p-6 text-white text-center relative overflow-hidden`}>
            {/* Background Decorations */}
            <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 bg-white/5 rounded-full translate-y-8 -translate-x-8"></div>
            
            <div className="relative">
              <div className="text-4xl mb-2">{scoreLevel.emoji}</div>
              <h2 className="text-xl font-bold mb-2">训练完成！</h2>
              <div className="text-3xl font-bold mb-1">{finalScore}分</div>
              <div className="text-sm opacity-90">{scoreLevel.level}</div>
            </div>
          </div>

          {/* Content Area */}
          <div className="p-6 space-y-4">
            {/* Score Statistics */}
            <div className="text-center">
              <p className="text-slate-600 mb-4">答对 {correctCount} / {questions.length} 题</p>
              
              {/* Point Rewards */}
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-100">
                <div className="flex items-center justify-center gap-2 mb-3">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <Award className="w-4 h-4 text-purple-600" />
                  </div>
                  <span className="font-semibold text-purple-800">获得积分</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between text-slate-600">
                    <span>答题积分:</span>
                    <span className="font-medium">+{totalPoints}</span>
                  </div>
                  {bonusPoints > 0 && (
                    <div className="flex justify-between text-slate-600">
                      <span>表现奖励:</span>
                      <span className="font-medium text-green-600">+{bonusPoints}</span>
                    </div>
                  )}
                  <div className="border-t border-purple-200 pt-2">
                    <div className="flex justify-between font-bold text-purple-700">
                      <span>总计:</span>
                      <span>+{finalTotalPoints} 积分</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <Button 
                  variant="outline" 
                  onClick={() => window.location.reload()}
                  className="text-slate-700 border-slate-300 hover:bg-slate-50 hover:border-slate-400 transition-all duration-200"
                >
                  再来一次
                </Button>
                <Button 
                  onClick={() => navigate(createPageUrl('TrainingHub'))}
                  className={`bg-gradient-to-r ${scoreLevel.color} hover:opacity-90 text-white font-medium transition-all duration-200`}
                >
                  返回训练
                </Button>
              </div>
              
              <Button 
                onClick={() => navigate(createPageUrl('PointsShop'))}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium"
              >
                <Gift className="w-4 h-4 mr-2" />
                积分商城
              </Button>
            </div>

            {/* Encouragement Message */}
            <div className="text-center pt-2">
              {finalScore >= 90 ? (
                <p className="text-sm text-green-600 font-medium">🌟 表现优秀！继续保持！</p>
              ) : finalScore >= 80 ? (
                <p className="text-sm text-blue-600 font-medium">👏 做得不错！再接再厉！</p>
              ) : finalScore >= 70 ? (
                <p className="text-sm text-yellow-600 font-medium">💪 继续努力，你会更好！</p>
              ) : (
                <p className="text-sm text-red-600 font-medium">🔥 多练习几次，一定能提高！</p>
              )}
            </div>
          </div>
        </motion.div>

        {/* Bottom Hint */}
        {currentTopic && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }} 
            animate={{ opacity: 1, y: 0 }} 
            transition={{ delay: 0.5 }}
            className="mt-4 text-center"
          >
            <p className="text-sm text-slate-500">
              刚刚完成了 <span className="font-medium text-slate-700">{currentTopic.name}</span> 的训练
            </p>
          </motion.div>
        )}
      </div>
    );
  }

  // Calculate current score for header display
  const currentSessionScore = answers.reduce((sum, answer) => {
    const question = questions.find(q => q.id === answer.question_id);
    return sum + calculatePoints(answer.is_correct, question?.difficulty || 1);
  }, 0);

  const topicName = currentTopic?.name || '未知主题'; // For header display

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* 头部 - 采用更现代的蓝紫色渐变 */}
      <div className="bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 pt-8 pb-12 px-4 sticky top-0 z-10 overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute -top-1/2 -left-1/4 w-96 h-96 bg-white/5 rounded-full opacity-50"></div>
        <div className="absolute -bottom-1/2 -right-1/4 w-80 h-80 bg-white/5 rounded-full opacity-50"></div>
        
        <div className="max-w-md mx-auto relative">
          <div className="flex items-center gap-3 mb-4">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigate(-1)}
              className="text-white hover:bg-white/20"
            >
              <ChevronLeft className="w-5 h-5" />
            </Button>
            <div className="flex-1 min-w-0">
              <h1 className="text-white text-xl font-bold truncate">
                {topicName} 训练
              </h1>
            </div>
          </div>

          {/* 训练信息 - 增加玻璃拟态效果 */}
          <div className="flex items-center justify-between bg-white/20 backdrop-blur-sm p-3 rounded-xl text-sm text-purple-100 border border-white/20">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              <span>第 {currentQuestionIndex + 1} / {questions.length} 题</span>
            </div>
            <div className="flex items-center gap-2 font-semibold">
              <Zap className="w-4 h-4 text-yellow-300" />
              <span>{currentSessionScore} 分</span>
            </div>
          </div>

          {/* 鼓励语 - 移动到头部显眼位置 */}
          <div className="text-center mt-3">
            <p className="text-white/90 text-sm font-medium">✨ 日拱一卒，功不唐捐 ✨</p>
          </div>
        </div>
      </div>

      <div className="px-4 -mt-8 max-w-md mx-auto pb-32 relative z-20">
        {currentQuestion && (
          <AnimatePresence mode="wait">
            <motion.div 
              key={currentQuestion.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="shadow-2xl overflow-hidden border-0 bg-white/80 backdrop-blur-md">
                <CardContent className="p-4 sm:p-5">
                  <div className="flex items-start gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0 text-white shadow-md">
                      <Volume2 className="w-5 h-5" />
                    </div>
                    <p className="font-semibold text-slate-800 text-base leading-relaxed pt-1.5">{currentQuestion.content}</p>
                  </div>

                  {/* Question Type Content */}
                  {currentQuestion.type === 'choice' && currentQuestion.options && (
                    <div className="space-y-3">
                      {currentQuestion.options.map(option => (
                        <div
                          key={option.label}
                          onClick={() => handleOptionSelect(option)}
                          className={`flex items-start gap-3 p-3.5 rounded-xl cursor-pointer transition-all duration-200 border-2 ${
                            showResult
                              ? option.is_correct
                                ? 'bg-green-50 border-green-400'
                                : selectedOption?.label === option.label
                                ? 'bg-red-50 border-red-400'
                                : 'bg-white border-transparent'
                              : 'bg-white border-transparent hover:border-blue-400 hover:bg-blue-50/50'
                          }`}
                        >
                          <div className={`w-6 h-6 mt-0.5 rounded-md flex items-center justify-center flex-shrink-0 font-bold text-xs transition-all duration-300 ${
                            showResult && (option.is_correct || selectedOption?.label === option.label) ?
                              (option.is_correct ? 'bg-green-500 text-white' : 'bg-red-500 text-white') :
                              'bg-slate-200 text-slate-600'
                          }`}>
                            {showResult ? (option.is_correct ? <Check size={14} /> : <X size={14} />) : option.label}
                          </div>
                          <span className="flex-1 text-slate-700 text-sm">{option.content}</span>
                        </div>
                      ))}
                    </div>
                  )}

                  {currentQuestion.type === 'voice' && !showResult && (
                    <div className="space-y-4">
                      {/* 强化灵豆功能提示 */}
                      <div className="bg-gradient-to-r from-orange-50 to-amber-50 border border-orange-200 rounded-xl p-4">
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            <Gem className="w-5 h-5 text-orange-500" />
                            <span className="font-semibold text-orange-800">🎯 想要高分回答？</span>
                          </div>
                          <p className="text-sm text-orange-700 mb-3">
                            使用AI灵感功能，获得专业的回答思路，让你的表现更出色！
                          </p>
                          <Button 
                            onClick={handleGetInspiration} 
                            disabled={isLoadingInspiration} 
                            className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-medium shadow-lg"
                            size="sm"
                          >
                            {isLoadingInspiration ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                AI思考中...
                              </>
                            ) : (
                              <>
                                <Sparkles className="mr-2 h-4 w-4" />
                                获取AI灵感
                                <div className="ml-2 flex items-center gap-1 text-xs bg-white/20 px-2 py-1 rounded">
                                  <Zap className="w-3 h-3" />
                                  1灵豆
                                </div>
                              </>
                            )}
                          </Button>
                          <div className="text-xs text-orange-600 mt-2 flex items-center justify-center gap-1">
                            <Gem className="w-3 h-3" />
                            当前灵豆余额: {user?.lingdou_balance || 0}
                          </div>
                        </div>
                      </div>
                      
                      {/* AI Inspiration Display */}
                      {aiInspiration && (
                        <motion.div 
                          initial={{opacity:0, y: -10}} 
                          animate={{opacity:1, y:0}} 
                          className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl"
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <Sparkles className="w-4 h-4 text-blue-500" />
                            <span className="text-sm font-medium text-blue-700">💡 AI回答思路</span>
                          </div>
                          <p className="text-sm text-blue-800 whitespace-pre-line leading-relaxed">
                            {aiInspiration}
                          </p>
                        </motion.div>
                      )}
                      
                      {/* Input Method Switch & Input Area */}
                      <div className="bg-slate-50 p-3 rounded-xl space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-slate-700">请回答</span>
                          <div className="bg-white p-1 rounded-lg flex shadow-sm">
                            <Button
                              variant={inputMode === 'text' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setInputMode('text')}
                              className={`${inputMode === 'text' ? 'bg-blue-600 text-white shadow-sm' : 'text-slate-600 hover:bg-slate-100'} px-3 py-1.5 text-xs rounded-md`}
                            >
                              <BrainCircuit className="w-3 h-3 mr-1" />
                              文字输入
                            </Button>
                            <Button
                              variant={inputMode === 'voice' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setInputMode('voice')}
                              className={`${inputMode === 'voice' ? 'bg-blue-600 text-white shadow-sm' : 'text-slate-600 hover:bg-slate-100'} px-3 py-1.5 text-xs rounded-md`}
                            >
                              <Mic className="w-3 h-3 mr-1" />
                              语音输入
                            </Button>
                          </div>
                        </div>
                        
                        <div className="relative">
                          <textarea
                            value={transcript}
                            onChange={(e) => setTranscript(e.target.value)}
                            placeholder={inputMode === 'voice' ? "点击右侧麦克风开始录音..." : "请在这里输入您的回答..."}
                            className="w-full h-32 p-3 pr-14 border-2 border-slate-200 bg-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition text-sm resize-none"
                          />
                          {inputMode === 'voice' && (
                            <Button
                              size="icon"
                              onClick={() => {
                                // 这里添加语音录制逻辑
                                setTranscript("这是模拟的语音转文字结果，用于演示...");
                              }}
                              className="absolute top-1/2 right-2.5 -translate-y-1/2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full w-10 h-10 shadow-lg"
                            >
                              <Mic className="w-5 h-5" />
                            </Button>
                          )}
                        </div>
                        <div className="text-xs text-slate-500 text-right">
                          {transcript.length}/500 字符
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Result/Feedback Section */}
                  {showResult && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mt-6"
                    >
                      {currentQuestion.type === 'choice' && (
                        <div className="p-4 bg-slate-50/70 rounded-lg border border-slate-200">
                          <h4 className="font-bold mb-2 text-slate-800">答案解析</h4>
                          <p className="text-sm text-slate-700 whitespace-pre-line">{currentQuestion.analysis}</p>
                        </div>
                      )}
                      {currentQuestion.type === 'voice' && (
                        <AIFeedback 
                          feedback={aiFeedback} 
                          userAnswer={transcript}
                          inputMode={inputMode}
                        />
                      )}
                    </motion.div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </AnimatePresence>
        )}
      </div>

      {/* Sticky Bottom Button Bar - 智能显示逻辑 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm border-t border-slate-200/60 p-4 z-30">
        <div className="max-w-md mx-auto">
          {/* AI题目未提交状态 - 仅显示导航 */}
          {currentQuestion?.type === 'voice' && !showResult && (
            <div className="flex items-center justify-between gap-3">
              <Button
                onClick={handlePreviousQuestion}
                variant="outline"
                className="flex-1 h-12 text-base touch-manipulation"
                disabled={currentQuestionIndex === 0}
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                上一题
              </Button>
              
              <div className="flex-1 text-center">
                {transcript.length >= 10 ? (
                  <Button 
                    onClick={handleVoiceSubmit}
                    disabled={isProcessingAI}
                    className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 h-12 text-base font-medium rounded-xl shadow-lg"
                  >
                    {isProcessingAI ? (
                      <>
                        <Loader2 className="animate-spin mr-2 h-5 w-5" />
                        AI分析中...
                      </>
                    ) : (
                      <>
                        <Send className="mr-2 h-5 w-5" />
                        提交回答
                      </>
                    )}
                  </Button>
                ) : (
                  <span className="text-sm text-slate-500 flex items-center justify-center gap-1">
                    <BrainCircuit className="w-4 h-4" />
                    请输入至少10个字符
                  </span>
                )}
              </div>

              <Button
                onClick={handleNextQuestion}
                className="flex-1 bg-slate-400 hover:bg-slate-500 h-12 text-base touch-manipulation opacity-50 cursor-not-allowed"
                disabled
              >
                {currentQuestionIndex < questions.length - 1 ? '下一题' : '完成训练'}
              </Button>
            </div>
          )}

          {/* AI题目已提交状态 - 强化重新回答 */}
          {currentQuestion?.type === 'voice' && showResult && (
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-2">
                <Button
                  onClick={handlePreviousQuestion}
                  variant="outline"
                  className="h-10 text-sm touch-manipulation"
                  disabled={currentQuestionIndex === 0}
                >
                  <ChevronLeft className="w-4 h-4 mr-1" />
                  上一题
                </Button>
                
                <Button
                  onClick={handleRetryQuestion}
                  className="h-10 text-sm bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-medium touch-manipulation"
                >
                  <Sparkles className="w-4 h-4 mr-1" />
                  重新回答
                </Button>

                <Button
                  onClick={handleNextQuestion}
                  className="h-10 text-sm bg-blue-600 hover:bg-blue-700 touch-manipulation"
                >
                  {currentQuestionIndex < questions.length - 1 ? '下一题' : '完成'}
                  {currentQuestionIndex < questions.length - 1 && <ChevronRight className="w-4 h-4 ml-1" />}
                </Button>
              </div>
              
              {/* 额外鼓励文案 */}
              <div className="text-center">
                <p className="text-xs text-slate-500">
                  💪 不满意这次回答？点击"重新回答"挑战更高分！
                </p>
              </div>
            </div>
          )}

          {/* 选择题状态 - 保持原有逻辑 */}
          {currentQuestion?.type === 'choice' && (
            <div className="flex items-center justify-between gap-3">
              <Button
                onClick={handlePreviousQuestion}
                variant="outline"
                className="flex-1 h-12 text-base touch-manipulation"
                disabled={currentQuestionIndex === 0}
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                上一题
              </Button>
              
              <div className="flex-1 text-center">
                <span className="text-sm text-slate-500">
                  {showResult ? '已完成回答' : '请选择答案'}
                </span>
              </div>

              <Button
                onClick={handleNextQuestion}
                className="flex-1 bg-blue-600 hover:bg-blue-700 h-12 text-base touch-manipulation"
                disabled={!showResult}
              >
                {currentQuestionIndex < questions.length - 1 ? '下一题' : '完成训练'}
                {currentQuestionIndex < questions.length - 1 && <ChevronRight className="w-4 h-4 ml-1" />}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
